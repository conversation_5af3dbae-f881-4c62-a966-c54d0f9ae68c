/**
 * @file 导出组件模块 - Export Components Module
 * @description 发票/收据生成器的导出功能独立模块，包含PDF和图片导出的所有组件
 * <AUTHOR> Team
 * @version 3.0
 * @date 2024-12-21
 * 
 * 主要功能：
 * - 300DPI高质量PDF/图片导出
 * - A4尺寸精确控制(794x1123px)
 * - 页眉/页脚图片处理(90px/70px)
 * - 印章透明度控制(0.9)
 * - 多订单支持
 * - 样式一致性保证
 */

// #region 导出配置模块
/**
 * 导出系统配置
 * @description 统一的导出配置，包含A4尺寸、DPI设置、质量参数等
 */
const ExportConfig = {
    // A4页面尺寸 (210mm x 297mm)
    a4: {
        width: 210, // mm
        height: 297, // mm
        widthPx: 794, // px at 96 DPI
        heightPx: 1123 // px at 96 DPI
    },
    
    // 300DPI高质量设置
    dpi: 300,
    quality: {
        scale: 3.125, // 300DPI / 96DPI = 3.125
        pngQuality: 1.0,
        jpegQuality: 0.95
    },
    
    // 图片尺寸标准
    images: {
        header: { height: 90 }, // 页眉固定高度90px
        footer: { height: 70 }  // 页脚固定高度70px
    },
    
    // 印章配置 - 修改透明度为0.9
    stamp: {
        opacity: 0.9, // 印章透明度设置为0.9
        width: 120,
        height: 120,
        bottomOffset: '15%',
        rightOffset: '5%'
    },
    
    // 导出状态管理
    state: {
        isExporting: false,
        currentProgress: 0,
        currentOperation: ''
    },
    
    // 边距设置
    margins: {
        left: 10, // 左边距10px
        right: 10, // 右边距10px
        top: 20,
        bottom: 20
    },

    // 性能和超时配置
    timeout: 60000,             // 导出超时时间（毫秒）
    maxRetries: 3,              // 最大重试次数
    retryDelay: 2000,           // 重试延迟（毫秒）

    // 内存管理配置
    memory: {
        maxUsagePercent: 0.8,   // 最大内存使用百分比
        cleanupThreshold: 10,   // 清理阈值（导出次数）
        forceGC: false          // 是否强制垃圾回收
    },

    // 导出质量配置
    quality: {
        scale: 3.125,           // 缩放比例 (794 * 3.125 = 2481px，约300DPI)
        jpegQuality: 0.95       // JPEG质量 (0-1)
    }
};
// #endregion

// #region 调试管理器
/**
 * 调试和日志管理器
 * @description 统一管理导出过程中的调试信息和错误处理
 */
const DebugManager = {
    // 调试模式开关
    debugMode: true,
    
    // 日志级别
    logLevels: {
        ERROR: 0,
        WARN: 1,
        INFO: 2,
        DEBUG: 3
    },
    
    currentLogLevel: 3, // DEBUG级别
    
    // 性能监控
    performanceMetrics: {},
    
    /**
     * 初始化调试管理器
     * @function init - 初始化调试系统
     */
    init() {
        if (this.debugMode) {
            console.log('🔧 调试管理器已启用');
        }
        return this;
    },
    
    /**
     * 记录日志
     * @function log - 统一的日志记录方法
     * @param {string} level - 日志级别 ('ERROR', 'WARN', 'INFO', 'DEBUG')
     * @param {string} message - 日志消息
     * @param {object} data - 附加数据
     */
    log(level, message, data = null) {
        if (!this.debugMode) return;
        
        const levelValue = this.logLevels[level] || 0;
        if (levelValue > this.currentLogLevel) return;
        
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level}] [导出系统]`;
        
        if (data) {
            console.log(`${prefix} ${message}`, data);
        } else {
            console.log(`${prefix} ${message}`);
        }
    },
    
    /**
     * 开始性能监控
     * @function startPerformanceMetric - 开始监控性能指标
     * @param {string} name - 指标名称
     */
    startPerformanceMetric(name) {
        this.performanceMetrics[name] = {
            startTime: performance.now(),
            endTime: null,
            duration: null
        };
    },
    
    /**
     * 结束性能监控
     * @function endPerformanceMetric - 结束性能监控并返回耗时
     * @param {string} name - 指标名称
     * @returns {number} 耗时（毫秒）
     */
    endPerformanceMetric(name) {
        const metric = this.performanceMetrics[name];
        if (!metric) return 0;
        
        metric.endTime = performance.now();
        metric.duration = metric.endTime - metric.startTime;
        
        this.log('DEBUG', `性能指标: ${name}`, {
            耗时: `${metric.duration.toFixed(2)}ms`
        });
        
        return metric.duration;
    }
};
// #endregion

// #region 工具函数模块
/**
 * 生成导出文件名
 * @function generateExportFilename - 统一的文件名生成逻辑
 * @param {string} format - 文件格式 ('pdf', 'png', 'jpeg')
 * @param {object} data - 表单数据对象（可选）
 * @returns {string} 格式化的文件名
 */
function generateExportFilename(format, data = null) {
    try {
        // 如果没有传入数据，尝试从全局函数获取
        if (!data && typeof collectFormData === 'function') {
            data = collectFormData();
        }
        
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const docTypeName = data?.documentType === 'invoice' ? '发票_Invoice' : '收据_Receipt';
        const docNumber = data?.documentNumber || timestamp;
        
        // 支持多订单模式的文件名
        let filename = `${docTypeName}_${docNumber}_300DPI.${format}`;
        
        // 如果是多订单模式，添加订单数量标识
        if (data?.multiOrderMode && data?.multiOrderData?.length > 1) {
            filename = `${docTypeName}_${data.multiOrderData.length}Orders_${docNumber}_300DPI.${format}`;
        }
        
        return filename;
    } catch (error) {
        DebugManager.log('ERROR', '文件名生成失败', error);
        return `Export_${Date.now()}.${format}`;
    }
}

/**
 * 提取并嵌入CSS样式
 * @function extractAndEmbedCSS - 提取页面CSS样式用于导出
 * @returns {string} 嵌入式CSS字符串
 */
function extractAndEmbedCSS() {
    try {
        DebugManager.startPerformanceMetric('CSS提取');
        
        // 获取页面中的所有样式表
        const styleSheets = Array.from(document.styleSheets);
        let cssText = '';

        // 提取内联样式
        const styleElements = document.querySelectorAll('style');
        styleElements.forEach(style => {
            if (style.textContent) {
                cssText += style.textContent + '\n';
            }
        });

        // 提取外部样式表（如果可访问）
        styleSheets.forEach(sheet => {
            try {
                if (sheet.cssRules) {
                    Array.from(sheet.cssRules).forEach(rule => {
                        cssText += rule.cssText + '\n';
                    });
                }
            } catch (e) {
                // 跨域样式表无法访问，跳过
                DebugManager.log('WARN', '无法访问样式表', { href: sheet.href });
            }
        });

        // 添加导出专用样式
        cssText += `
            /* 导出模式专用样式 */
            .export-mode {
                transform: none !important;
                scale: none !important;
                zoom: 1 !important;
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
                border: none !important;
                width: ${ExportConfig.a4.widthPx}px !important;
                min-height: ${ExportConfig.a4.heightPx}px !important;
            }
            
            /* 完全隐藏占位符 */
            .export-mode .image-placeholder,
            .export-mode .header-placeholder,
            .export-mode .footer-placeholder,
            .export-mode .stamp-placeholder {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                width: 0 !important;
                height: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            
            /* 印章透明度设置 */
            .export-mode .company-stamp {
                opacity: ${ExportConfig.stamp.opacity} !important;
            }
            
            /* 左右边距设置 */
            .export-mode .items-table,
            .export-mode .notes-section {
                padding-left: ${ExportConfig.margins.left}px !important;
                padding-right: ${ExportConfig.margins.right}px !important;
            }
            
            /* 文字排版优化 */
            .export-mode * {
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
                text-rendering: optimizeLegibility !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                box-sizing: border-box !important;
            }
        `;

        DebugManager.endPerformanceMetric('CSS提取');
        DebugManager.log('DEBUG', 'CSS样式提取完成', {
            样式长度: cssText.length,
            样式表数量: styleSheets.length,
            内联样式数量: styleElements.length
        });

        return cssText;
    } catch (error) {
        DebugManager.log('ERROR', 'CSS样式提取失败', error);
        return '';
    }
}

/**
 * 优化字体渲染
 * @function optimizeFontRendering - 为导出优化字体渲染质量
 * @param {HTMLElement} container - 容器元素
 */
function optimizeFontRendering(container) {
    try {
        DebugManager.startPerformanceMetric('字体优化');

        // 为所有文本元素应用高质量字体渲染
        const textElements = container.querySelectorAll('*');

        textElements.forEach(element => {
            // 应用字体渲染优化
            element.style.fontSmooth = 'always';
            element.style.webkitFontSmoothing = 'antialiased';
            element.style.mozOsxFontSmoothing = 'grayscale';
            element.style.textRendering = 'optimizeLegibility';

            // 确保中英文字体fallback
            if (element.style.fontFamily) {
                element.style.fontFamily = `${element.style.fontFamily}, "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif`;
            } else {
                element.style.fontFamily = '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif';
            }

            // 应用硬件加速
            element.style.webkitTransform = 'translateZ(0)';
            element.style.transform = 'translateZ(0)';
        });

        DebugManager.endPerformanceMetric('字体优化');
        DebugManager.log('DEBUG', `字体渲染优化完成，处理了${textElements.length}个元素`);

    } catch (error) {
        DebugManager.log('ERROR', '字体渲染优化失败', error);
    }
}

/**
 * 预处理图片
 * @function preprocessImages - 预处理页眉页脚图片，确保高质量
 * @param {HTMLElement} container - 容器元素
 */
async function preprocessImages(container) {
    try {
        DebugManager.startPerformanceMetric('图片预处理');

        const images = container.querySelectorAll('img');
        const loadPromises = [];

        images.forEach(img => {
            // 应用高质量样式
            img.style.imageRendering = 'high-quality';
            img.style.imageRendering = '-webkit-optimize-contrast';
            img.style.webkitBackfaceVisibility = 'hidden';
            img.style.backfaceVisibility = 'hidden';
            img.style.transform = 'translateZ(0)';

            // 确保图片完全填充容器 - 与预览样式保持一致
            if (img.closest('.document-header-image-container')) {
                img.style.height = '100%';
                img.style.width = '100%';
                img.style.maxWidth = 'none';
                img.style.objectFit = 'cover';
                img.style.objectPosition = 'center';
            }

            if (img.closest('.unified-document-footer.company-footer-image-container')) {
                img.style.height = '100%';
                img.style.width = '100%';
                img.style.maxWidth = 'none';
                img.style.objectFit = 'cover';
                img.style.objectPosition = 'center';
            }

            // 印章图片处理
            if (img.closest('.company-stamp')) {
                img.style.opacity = ExportConfig.stamp.opacity;
            }

            // 确保图片加载完成
            if (!img.complete) {
                loadPromises.push(new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = reject;
                    // 设置超时
                    setTimeout(() => reject(new Error('图片加载超时')), 5000);
                }));
            }
        });

        // 等待所有图片加载完成
        if (loadPromises.length > 0) {
            try {
                await Promise.all(loadPromises);
                DebugManager.log('DEBUG', '所有图片预处理完成');
            } catch (error) {
                DebugManager.log('WARN', '部分图片加载失败，继续导出', error);
            }
        }

        DebugManager.endPerformanceMetric('图片预处理');
        DebugManager.log('DEBUG', `图片预处理完成，共处理${images.length}张图片`);
    } catch (error) {
        DebugManager.log('ERROR', '图片预处理失败', error);
    }
}

/**
 * 重新渲染内容以确保导出模式下的一致性
 * @function reRenderForExport - 在导出模式下重新渲染内容
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<void>} 重新渲染完成的Promise
 */
async function reRenderForExport(container) {
    try {
        DebugManager.log('DEBUG', '开始重新渲染内容以确保导出一致性');

        // 收集当前表单数据
        const data = typeof collectFormData === 'function' ? collectFormData() : {};

        // 修复flex gap问题 - html2canvas不完全支持gap属性
        const FLEX_GAP_QUERY = '[style*="display: flex"][style*="gap"], .export-mode .flex-gap-fix';
        container.querySelectorAll(FLEX_GAP_QUERY).forEach(flexEl => {
            const computed = window.getComputedStyle(flexEl);
            const gapValue = computed.columnGap || computed.gap || flexEl.style.columnGap || flexEl.style.gap;
            if (!gapValue) return;

            // 去掉自身 gap，防止被 html2canvas 忽略后产生零间距
            flexEl.style.columnGap = '0px';
            flexEl.style.gap = '0px';

            // 仅处理横向主轴，纵向 flex-wrap 的情况极少且不影响主要排版
            const children = Array.from(flexEl.children);
            children.forEach((child, index) => {
                if (index !== children.length - 1) {
                    // 为除最后一个元素外添加右侧margin
                    child.style.marginRight = gapValue;
                }
            });
        });

        DebugManager.log('DEBUG', '导出模式下内容重新渲染完成', {
            文档类型: data.documentType,
            导出模式: true,
            内容长度: container.innerHTML.length
        });

        // 等待DOM更新完成
        await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
        DebugManager.log('ERROR', '内容重新渲染失败', error);
        throw error;
    }
}

/**
 * 检查外部依赖库
 * @function checkExternalDependencies - 检查外部库的可用性
 * @returns {object} 依赖检查结果
 */
function checkExternalDependencies() {
    const dependencies = {
        html2canvas: typeof html2canvas !== 'undefined',
        jsPDF: typeof window.jspdf !== 'undefined'
    };

    DebugManager.log('DEBUG', '外部依赖检查', dependencies);
    return dependencies;
}
// #endregion

// #region PDF导出功能
/**
 * 导出为PDF
 * @function exportToPDF - 使用现代化方案导出PDF
 * @param {HTMLElement} container - 容器元素
 */
async function exportToPDF(container) {
    try {
        DebugManager.startPerformanceMetric('PDF导出');

        // 检查html2canvas是否可用
        if (typeof html2canvas === 'undefined') {
            throw new Error('html2canvas库未加载，无法生成PDF');
        }

        // 检查jsPDF是否可用
        if (typeof window.jspdf === 'undefined') {
            throw new Error('jsPDF库未加载，无法生成PDF');
        }

        // 获取容器实际尺寸用于调试
        const containerRect = container.getBoundingClientRect();
        DebugManager.log('DEBUG', '导出前容器尺寸检查', {
            容器ID: container.id,
            实际尺寸: `${containerRect.width}x${containerRect.height}px`,
            配置尺寸: `${ExportConfig.a4.widthPx}x${ExportConfig.a4.heightPx}px`,
            缩放比例: ExportConfig.quality.scale,
            导出模式: container.classList.contains('export-mode')
        });

        // 等待所有自定义字体加载完成，避免字体替换导致排版差异
        if (document.fonts && document.fonts.ready) {
            try {
                await document.fonts.ready;
                DebugManager.log('DEBUG', '所有字体已加载完毕，开始生成PDF');
            } catch (fontErr) {
                DebugManager.log('WARN', '等待字体加载过程中出现问题，继续导出', fontErr);
            }
        }

        // 调试：在html2canvas调用前检查容器状态
        DebugManager.log('DEBUG', 'html2canvas调用前容器检查', {
            容器存在: !!container,
            容器可见: container.offsetWidth > 0 && container.offsetHeight > 0,
            容器尺寸: `${container.offsetWidth}x${container.offsetHeight}px`,
            容器内容长度: container.innerHTML.length,
            容器内容预览: container.innerHTML.substring(0, 200) + '...',
            子元素数量: container.children.length
        });

        let canvas;
        try {
            DebugManager.log('DEBUG', '开始调用html2canvas');

            // 使用最简单的html2canvas配置
            const html2canvasOptions = {
                scale: 1,
                useCORS: false, // 禁用CORS避免问题
                allowTaint: true, // 允许污染画布
                backgroundColor: '#ffffff',
                logging: false, // 禁用内部日志避免干扰
                removeContainer: false,
                foreignObjectRendering: false, // 禁用外部对象渲染
                imageTimeout: 0, // 禁用图片超时
                width: container.offsetWidth,
                height: container.offsetHeight
            };

            DebugManager.log('DEBUG', 'html2canvas配置', html2canvasOptions);

            // 添加超时处理
            const html2canvasPromise = html2canvas(container, html2canvasOptions);

            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('html2canvas超时 - 15秒')), 15000);
            });

            canvas = await Promise.race([html2canvasPromise, timeoutPromise]);
            DebugManager.log('DEBUG', 'html2canvas调用成功完成');
        } catch (html2canvasError) {
            DebugManager.log('ERROR', 'html2canvas调用失败', {
                错误消息: html2canvasError.message,
                错误类型: html2canvasError.name,
                错误堆栈: html2canvasError.stack?.substring(0, 500)
            });
            throw new Error(`html2canvas处理失败: ${html2canvasError.message}`);
        }

        // 调试：检查生成的canvas
        DebugManager.log('DEBUG', 'html2canvas生成完成', {
            Canvas存在: !!canvas,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            Canvas为空: canvas.width === 0 || canvas.height === 0
        });

        // 创建PDF
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4',
            compress: false,
            precision: 16
        });

        // 获取PDF页面尺寸
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        // 计算图片在PDF中的尺寸（1:1映射）
        const imgWidth = pdfWidth;
        const imgHeight = pdfHeight;
        const x = 0;
        const y = 0;

        // 将Canvas转换为图片数据
        const imgData = canvas.toDataURL('image/png');

        // 添加图片到PDF
        pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight, undefined, 'FAST');

        // 添加详细的调试日志
        DebugManager.log('DEBUG', 'PDF导出尺寸详情', {
            Canvas实际尺寸: `${canvas.width}x${canvas.height}px`,
            Canvas缩放比例: ExportConfig.quality.scale,
            PDF页面尺寸: `${pdfWidth}x${pdfHeight}mm`,
            图片在PDF中尺寸: `${imgWidth}x${imgHeight}mm`,
            图片位置: `(${x}, ${y})mm`,
            无缩放映射: '1:1直接映射到A4'
        });

        // 生成文件名
        const filename = generateExportFilename('pdf');

        // 保存PDF
        pdf.save(filename);

        DebugManager.endPerformanceMetric('PDF导出');
        DebugManager.log('INFO', 'PDF导出完成', {
            文件名: filename,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            PDF尺寸: `${imgWidth.toFixed(2)}x${imgHeight.toFixed(2)}mm`,
            分辨率: `${ExportConfig.dpi}DPI`,
            修复状态: '已移除二次缩放，使用1:1映射'
        });
    } catch (error) {
        DebugManager.log('ERROR', 'PDF导出失败', error);
        throw error;
    }
}
// #endregion

// #region 图片导出功能
/**
 * 导出为图片
 * @function exportToImage - 导出为PNG或JPEG图片
 * @param {HTMLElement} container - 容器元素
 * @param {string} format - 图片格式 ('png' 或 'jpeg')
 */
async function exportToImage(container, format) {
    try {
        DebugManager.startPerformanceMetric('图片导出');

        // 检查html2canvas是否可用
        if (typeof html2canvas === 'undefined') {
            throw new Error('html2canvas库未加载，无法生成图片');
        }

        // 获取容器实际尺寸用于调试
        const containerRect = container.getBoundingClientRect();
        DebugManager.log('DEBUG', '图片导出前容器尺寸检查', {
            容器ID: container.id,
            实际尺寸: `${containerRect.width}x${containerRect.height}px`,
            配置尺寸: `${ExportConfig.a4.widthPx}x${ExportConfig.a4.heightPx}px`,
            缩放比例: ExportConfig.quality.scale,
            导出格式: format,
            导出模式: container.classList.contains('export-mode')
        });

        // 等待字体加载完成，确保渲染一致
        if (document.fonts && document.fonts.ready) {
            try {
                await document.fonts.ready;
                DebugManager.log('DEBUG', '所有字体已加载完毕，开始生成图片');
            } catch (fontErr) {
                DebugManager.log('WARN', '等待字体加载过程中出现问题，继续导出', fontErr);
            }
        }

        // 调试：在html2canvas调用前检查容器状态
        DebugManager.log('DEBUG', 'html2canvas图片导出前容器检查', {
            容器存在: !!container,
            容器可见: container.offsetWidth > 0 && container.offsetHeight > 0,
            容器尺寸: `${container.offsetWidth}x${container.offsetHeight}px`,
            容器内容长度: container.innerHTML.length,
            容器内容预览: container.innerHTML.substring(0, 200) + '...',
            子元素数量: container.children.length
        });

        let canvas;
        try {
            DebugManager.log('DEBUG', '开始调用html2canvas进行图片导出');

            // 使用最简单的html2canvas配置
            const html2canvasOptions = {
                scale: 1,
                useCORS: false, // 禁用CORS避免问题
                allowTaint: true, // 允许污染画布
                backgroundColor: '#ffffff',
                logging: false, // 禁用内部日志避免干扰
                removeContainer: false,
                foreignObjectRendering: false, // 禁用外部对象渲染
                imageTimeout: 0, // 禁用图片超时
                width: container.offsetWidth,
                height: container.offsetHeight
            };

            DebugManager.log('DEBUG', 'html2canvas配置', html2canvasOptions);

            // 添加超时处理
            const html2canvasPromise = html2canvas(container, html2canvasOptions);

            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('html2canvas超时 - 15秒')), 15000);
            });

            canvas = await Promise.race([html2canvasPromise, timeoutPromise]);
            DebugManager.log('DEBUG', 'html2canvas图片导出调用成功完成');
        } catch (html2canvasError) {
            DebugManager.log('ERROR', 'html2canvas图片导出调用失败', {
                错误消息: html2canvasError.message,
                错误类型: html2canvasError.name,
                错误堆栈: html2canvasError.stack?.substring(0, 500)
            });
            throw new Error(`html2canvas图片处理失败: ${html2canvasError.message}`);
        }

        // 调试：检查生成的canvas
        DebugManager.log('DEBUG', 'html2canvas图片生成完成', {
            Canvas存在: !!canvas,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            Canvas为空: canvas.width === 0 || canvas.height === 0
        });

        // 生成图片数据
        let imageData;
        if (format === 'jpeg') {
            imageData = canvas.toDataURL('image/jpeg', ExportConfig.quality.jpegQuality);
        } else {
            imageData = canvas.toDataURL('image/png');
        }

        // 生成文件名
        const filename = generateExportFilename(format);

        // 创建下载链接
        const link = document.createElement('a');
        link.download = filename;
        link.href = imageData;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        DebugManager.endPerformanceMetric('图片导出');
        DebugManager.log('INFO', `${format.toUpperCase()}图片导出完成`, {
            文件名: filename,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            数据大小: `${Math.round(imageData.length / 1024)}KB`,
            分辨率: `${ExportConfig.dpi}DPI`,
            修复状态: '已移除固定尺寸限制，使用容器实际尺寸',
            质量设置: format === 'jpeg' ? `JPEG质量${ExportConfig.quality.jpegQuality}` : 'PNG无损'
        });
    } catch (error) {
        DebugManager.log('ERROR', '图片导出失败', error);
        throw error;
    }
}
// #endregion

// #region ModernExportSystem核心模块
/**
 * 现代化高质量导出系统
 * @description 基于原生浏览器API的全新导出方案，支持300DPI高质量输出
 */
const ModernExportSystem = {
    // 导出配置（引用全局配置）
    config: ExportConfig,

    // 导出状态管理
    state: ExportConfig.state,

    /**
     * 初始化导出系统
     * @function init - 初始化现代化导出系统
     */
    init() {
        this.createExportUI();
        this.bindEvents();
        DebugManager.log('INFO', '现代化导出系统初始化完成');
    },

    /**
     * 创建导出UI界面
     * @function createExportUI - 创建简洁的导出界面，确保只在预览模组中创建
     */
    createExportUI() {
        // 只查找预览区域中的导出按钮容器，避免重复创建
        const previewSection = document.querySelector('.preview-section');
        if (!previewSection) {
            DebugManager.log('WARN', '未找到预览区域');
            return;
        }

        const exportButtonsContainer = previewSection.querySelector('.btn-group');
        if (!exportButtonsContainer) {
            DebugManager.log('WARN', '未找到预览区域中的导出按钮容器');
            return;
        }

        // 检查是否已经存在现代化导出UI，避免重复创建
        if (exportButtonsContainer.querySelector('.modern-export-ui')) {
            DebugManager.log('INFO', '现代化导出UI已存在，跳过创建');
            return;
        }

        // 查找并移除旧的导出相关元素
        const oldExportElements = [
            exportButtonsContainer.querySelector('#export-method-selector'),
            exportButtonsContainer.querySelector('button[onclick*="exportToPDF"]'),
            exportButtonsContainer.querySelector('button[onclick*="exportToImage"]'),
            exportButtonsContainer.querySelector('button[onclick*="debugImageExport"]'),
            exportButtonsContainer.querySelector('#export-method-info'),
            exportButtonsContainer.querySelector('.modern-export-ui')
        ];

        oldExportElements.forEach(element => {
            if (element) {
                element.remove();
            }
        });

        // 创建简化的导出UI - 只保留核心按钮
        const exportUI = document.createElement('div');
        exportUI.className = 'simple-export-ui';
        exportUI.innerHTML = `
            <div class="export-buttons-row">
                <button type="button" class="btn btn-success export-btn" data-format="pdf">
                    导出PDF / Export PDF
                </button>
                <button type="button" class="btn btn-success export-btn" data-format="png">
                    导出PNG / Export PNG
                </button>
                <button type="button" class="btn btn-success export-btn" data-format="jpeg">
                    导出JPEG / Export JPEG
                </button>
                <button type="button" class="btn btn-secondary" onclick="debugImageExport()" title="诊断图片导出问题">
                    调试 / Debug
                </button>
            </div>
        `;

        // 将简化的导出UI添加到容器中
        exportButtonsContainer.appendChild(exportUI);

        DebugManager.log('INFO', '现代化导出UI已创建（仅在预览区域）');
    },

    /**
     * 绑定事件处理
     * @function bindEvents - 绑定导出按钮事件
     */
    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('export-btn')) {
                const format = e.target.dataset.format;
                if (format) {
                    this.startExport(format);
                }
            }
        });
    },

    /**
     * 开始导出流程（增强版本 - 集成性能优化和错误处理）
     * @function startExport - 开始导出流程
     * @param {string} format - 导出格式 ('pdf', 'png', 'jpeg')
     */
    async startExport(format) {
        if (this.state.isExporting) {
            DebugManager.log('WARN', '导出正在进行中，请稍候');
            return;
        }

        // 开始性能监控
        const performanceSession = PerformanceOptimizer.startMonitoring(`${format.toUpperCase()}导出`);
        let previewContainer = null;
        let originalContent = null;
        let originalClassName = null;

        try {
            this.state.isExporting = true;
            DebugManager.log('INFO', `开始${format.toUpperCase()}导出`);

            // 预检查：验证依赖和环境
            await this.preExportValidation(format);

            // 修复：确保预览内容与表单数据完全同步
            DebugManager.log('DEBUG', '开始预览同步检查');

            // 强制刷新DOM缓存，确保数据一致性
            if (typeof refreshDOMCache === 'function') {
                refreshDOMCache();
            }

            // 更新预览并等待完成
            await this.ensurePreviewSync();

            // 获取导出容器
            const container = document.getElementById('document-container');
            if (!container || container.innerHTML.trim().length === 0) {
                throw new Error('预览内容为空，请先填写表单内容');
            }

            // 验证预览内容与表单数据的一致性
            await this.validateContentConsistency(container);

            // 收集当前表单数据
            const formDataForExport = typeof collectFormData === 'function' ? collectFormData() : {};

            // 渲染导出模式 HTML
            let exportHtml = '';
            if (formDataForExport.documentType === 'invoice') {
                if (typeof InvoiceTemplate !== 'undefined' && InvoiceTemplate.render) {
                    exportHtml = InvoiceTemplate.render(formDataForExport, true);
                }
            } else {
                if (typeof ReceiptTemplate !== 'undefined' && ReceiptTemplate.render) {
                    exportHtml = ReceiptTemplate.render(formDataForExport, true);
                }
            }

            // 嵌入CSS样式到导出HTML
            const embeddedCSS = extractAndEmbedCSS();
            exportHtml = `
                <style>${embeddedCSS}</style>
                <div id="document-container">
                    ${exportHtml}
                </div>
            `;

            // 简化方案：直接使用预览容器进行导出
            previewContainer = document.getElementById('document-container');
            if (!previewContainer) {
                throw new Error('预览容器不存在，无法导出');
            }

            // 临时保存原始内容和类名
            originalContent = previewContainer.innerHTML;
            originalClassName = previewContainer.className;

            // 设置导出模式内容和样式
            previewContainer.innerHTML = exportHtml;
            previewContainer.className = originalClassName + ' export-mode';

            // 调试：检查导出容器内容
            DebugManager.log('DEBUG', '预览容器导出内容设置完成', {
                容器ID: previewContainer.id,
                HTML长度: exportHtml.length,
                HTML前100字符: exportHtml.substring(0, 100),
                容器尺寸: `${previewContainer.offsetWidth}x${previewContainer.offsetHeight}px`,
                子元素数量: previewContainer.children.length,
                容器可见: previewContainer.offsetWidth > 0 && previewContainer.offsetHeight > 0
            });

            // 使用预览容器作为导出容器
            const hiddenContainer = previewContainer;

            // 字体优化 & 图片预处理
            optimizeFontRendering(hiddenContainer);
            await preprocessImages(hiddenContainer);

            // 导出（带超时保护）
            await this.performExportWithTimeout(format, hiddenContainer);

            DebugManager.log('INFO', `${format.toUpperCase()}导出完成`);

        } catch (error) {
            // 使用增强的错误处理
            const errorResult = ErrorHandler.handleExportError(error, `${format}导出`, {
                format,
                hasContainer: !!previewContainer,
                containerSize: previewContainer ? `${previewContainer.offsetWidth}x${previewContainer.offsetHeight}` : 'N/A'
            });

            // 如果可以重试，提供重试选项
            if (errorResult.canRetry) {
                DebugManager.log('INFO', '错误可重试，等待用户决定');
            }

        } finally {
            // 恢复预览容器的原始内容
            if (previewContainer && originalContent && originalClassName) {
                try {
                    previewContainer.innerHTML = originalContent;
                    previewContainer.className = originalClassName;
                } catch (restoreError) {
                    DebugManager.log('ERROR', '恢复预览内容失败', restoreError);
                }
            }

            // 额外的恢复检查
            try {
                const container = document.getElementById('document-container');
                if (container && container.classList.contains('export-mode')) {
                    container.classList.remove('export-mode');
                    await this.restorePreviewContent(container);
                }
            } catch (restoreErr) {
                DebugManager.log('WARN', '导出后恢复预览时发生异常', restoreErr);
            }

            // 结束性能监控
            const performanceReport = PerformanceOptimizer.endMonitoring(performanceSession);

            // 清理内存
            PerformanceOptimizer.cleanup();

            this.state.isExporting = false;
        }
    },

    /**
     * 预导出验证
     * @function preExportValidation - 导出前的环境和依赖验证
     * @param {string} format - 导出格式
     */
    async preExportValidation(format) {
        // 检查依赖库
        const dependencies = checkExternalDependencies();

        if (!dependencies.html2canvas) {
            throw new Error('html2canvas库未加载，无法进行导出');
        }

        if (format === 'pdf' && !dependencies.jsPDF) {
            throw new Error('jsPDF库未加载，无法导出PDF');
        }

        // 检查内存使用情况
        const memory = PerformanceOptimizer.getMemoryUsage();
        if (memory.used > memory.limit * 0.9) {
            DebugManager.log('WARN', '内存使用率过高，可能影响导出性能', memory);
        }

        // 检查浏览器兼容性
        if (!document.createElement('canvas').getContext) {
            throw new Error('浏览器不支持Canvas，无法进行导出');
        }

        DebugManager.log('DEBUG', '预导出验证通过', { format, dependencies, memory });
    },

    /**
     * 带超时保护的导出执行
     * @function performExportWithTimeout - 执行导出并提供超时保护
     * @param {string} format - 导出格式
     * @param {HTMLElement} container - 容器元素
     */
    async performExportWithTimeout(format, container) {
        const timeout = ExportConfig.timeout || 60000; // 默认60秒超时

        const exportPromise = format === 'pdf' ?
            exportToPDF(container) :
            exportToImage(container, format);

        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`导出超时：操作超过${timeout/1000}秒未完成`));
            }, timeout);
        });

        return Promise.race([exportPromise, timeoutPromise]);
    },

    /**
     * 确保预览同步
     * @function ensurePreviewSync - 确保预览内容与表单数据完全同步
     * @returns {Promise<void>} 同步完成的Promise
     */
    async ensurePreviewSync() {
        return new Promise((resolve) => {
            // 检查PreviewManager状态
            if (typeof PreviewManager !== 'undefined' && PreviewManager.isUpdating) {
                DebugManager.log('DEBUG', '等待预览更新完成...');

                // 等待预览更新完成
                const checkInterval = setInterval(() => {
                    if (!PreviewManager.isUpdating) {
                        clearInterval(checkInterval);
                        DebugManager.log('DEBUG', '预览更新已完成');
                        resolve();
                    }
                }, 50);

                // 超时保护
                setTimeout(() => {
                    clearInterval(checkInterval);
                    DebugManager.log('WARN', '预览同步等待超时，继续导出');
                    resolve();
                }, 2000);
            } else {
                // 直接解析，不强制更新预览以避免循环调用
                DebugManager.log('DEBUG', '跳过预览更新，直接继续导出');
                resolve();
            }
        });
    },

    /**
     * 验证内容一致性
     * @function validateContentConsistency - 验证预览内容与表单数据的一致性
     * @param {HTMLElement} container - 预览容器
     * @returns {Promise<void>} 验证完成的Promise
     */
    async validateContentConsistency(container) {
        try {
            // 收集当前表单数据
            const formData = typeof collectFormData === 'function' ? collectFormData() : {};

            // 分析预览容器内容
            const previewContent = {
                customerName: container.querySelector('.customer-name')?.textContent || '',
                itemsCount: container.querySelectorAll('.item-row').length,
                totalAmount: container.querySelector('.total-amount-container')?.textContent || ''
            };

            DebugManager.log('DEBUG', '内容一致性验证', {
                表单数据: {
                    客户名称: formData.customerName,
                    项目数量: formData.items?.length || 0,
                    总金额: formData.total
                },
                预览内容: previewContent,
                一致性检查: {
                    项目数量匹配: (formData.items?.length || 0) === previewContent.itemsCount,
                    总金额存在: !!previewContent.totalAmount
                }
            });

            // 如果发现不一致，强制重新渲染
            if ((formData.items?.length || 0) !== previewContent.itemsCount) {
                DebugManager.log('WARN', '检测到内容不一致，强制重新渲染');
                if (typeof updatePreview === 'function') {
                    updatePreview();
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
            }

        } catch (error) {
            DebugManager.log('ERROR', '内容一致性验证失败', error);
            // 验证失败不阻止导出，但记录错误
        }
    },

    /**
     * 恢复预览模式的内容
     * @function restorePreviewContent - 导出完成后恢复预览模式的内容
     * @param {HTMLElement} container - 容器元素
     * @returns {Promise<void>} 恢复完成的Promise
     */
    async restorePreviewContent(container) {
        try {
            DebugManager.log('DEBUG', '开始恢复预览模式内容');

            // 收集当前表单数据
            const data = typeof collectFormData === 'function' ? collectFormData() : {};

            // 使用预览模式重新渲染内容
            let html = '';
            if (data.documentType === 'invoice') {
                if (typeof InvoiceTemplate !== 'undefined' && InvoiceTemplate.render) {
                    html = InvoiceTemplate.render(data, false); // isExport = false
                }
            } else {
                if (typeof ReceiptTemplate !== 'undefined' && ReceiptTemplate.render) {
                    html = ReceiptTemplate.render(data, false); // isExport = false
                }
            }

            // 更新容器内容
            container.innerHTML = html;

            DebugManager.log('DEBUG', '预览模式内容恢复完成', {
                文档类型: data.documentType,
                导出模式: false,
                内容长度: html.length
            });

            // 等待DOM更新完成
            await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
            DebugManager.log('ERROR', '预览模式内容恢复失败', error);
            // 恢复失败时，尝试调用updatePreview
            try {
                if (typeof updatePreview === 'function') {
                    updatePreview();
                }
            } catch (fallbackError) {
                DebugManager.log('ERROR', '预览恢复备用方案也失败', fallbackError);
            }
        }
    }
};
// #endregion

// #region 调试工具
/**
 * 调试图片导出功能
 * @function debugImageExport - 调试图片导出功能，提供详细的诊断信息
 */
function debugImageExport() {
    console.log('🔍 开始图片导出调试...');

    const debugInfo = {
        html2canvas可用: typeof html2canvas !== 'undefined',
        html2canvas版本: typeof html2canvas !== 'undefined' ? html2canvas.version || '未知' : '未加载',
        jsPDF可用: typeof window.jspdf !== 'undefined',
        浏览器: navigator.userAgent,
        文档就绪状态: document.readyState,
        容器状态: {
            'document-container': !!document.getElementById('document-container'),
            'document-preview': !!document.getElementById('document-preview'),
            'container内容长度': document.getElementById('document-container')?.innerHTML?.length || 0,
            'preview内容长度': document.getElementById('document-preview')?.innerHTML?.length || 0
        },
        表单数据: (() => {
            try {
                return typeof collectFormData === 'function' ? collectFormData() : '函数不可用';
            } catch (e) {
                return `获取失败: ${e.message}`;
            }
        })(),
        导出配置: ExportConfig,
        ModernExportSystem状态: {
            可用: typeof ModernExportSystem !== 'undefined',
            正在导出: ModernExportSystem?.state?.isExporting || false
        },
        CSS变量检查: {
            'A4宽度': getComputedStyle(document.documentElement).getPropertyValue('--a4-width-px'),
            'A4高度': getComputedStyle(document.documentElement).getPropertyValue('--a4-height-px'),
            '预览缩放因子': getComputedStyle(document.documentElement).getPropertyValue('--preview-scale-factor')
        },
        修复状态: '✅ 已修复导出内容缩小问题：移除固定尺寸限制，添加导出模式缩放重置'
    };

    console.table(debugInfo);
    console.log('🔧 导出尺寸修复状态: 已移除固定尺寸限制，添加导出模式缩放重置');
    console.log('📏 修复详情:');
    console.log('  1. 添加了 .export-mode 类的缩放重置 CSS');
    console.log('  2. 移除了 html2canvas 的固定 width/height 限制');
    console.log('  3. 简化了 PDF 导出逻辑，使用 1:1 映射');
    console.log('  4. 增强了调试日志，便于问题定位');
    console.log('  5. 印章透明度设置为0.9');
    console.log('  6. 添加了10px左右边距');
    alert('调试信息已输出到浏览器控制台，请查看详细信息。\n\n✅ 修复状态: 已解决导出内容缩小问题');

    return debugInfo;
}
// #endregion

// #region 兼容性函数
/**
 * 导出为PDF（兼容性函数）
 * @function exportToPDFCompat - 兼容旧版本的导出PDF函数
 */
async function exportToPDFCompat() {
    return ModernExportSystem.startExport('pdf');
}

/**
 * 导出为图片（兼容性函数）
 * @function exportToImageCompat - 兼容旧版本的导出图片函数
 * @param {string} format - 图片格式 ('png' 或 'jpeg')，默认为 'png'
 */
async function exportToImageCompat(format = 'png') {
    return ModernExportSystem.startExport(format);
}

/**
 * 基础功能测试函数
 * @function testBasicFunctionality - 测试基础功能可用性
 */
function testBasicFunctionality() {
    console.log('⚙️ 测试基础功能...');

    try {
        const results = {
            exportConfigAvailable: typeof ExportConfig !== 'undefined',
            debugManagerAvailable: typeof DebugManager !== 'undefined',
            modernExportSystemAvailable: typeof ModernExportSystem !== 'undefined',
            generateExportFilenameFunction: typeof generateExportFilename === 'function',
            extractAndEmbedCSSFunction: typeof extractAndEmbedCSS === 'function',
            optimizeFontRenderingFunction: typeof optimizeFontRendering === 'function',
            preprocessImagesFunction: typeof preprocessImages === 'function',
            exportToPDFFunction: typeof exportToPDF === 'function',
            exportToImageFunction: typeof exportToImage === 'function',
            debugImageExportFunction: typeof debugImageExport === 'function',
            html2canvasAvailable: typeof html2canvas !== 'undefined',
            jsPDFAvailable: typeof window.jspdf !== 'undefined'
        };

        console.log('✅ 基础功能测试完成:', results);
        return results;

    } catch (error) {
        console.error('❌ 基础功能测试失败:', error);
        return { error: error.message };
    }
}
// #endregion

// #region 性能优化工具
/**
 * 性能优化管理器
 * @description 提供性能监控、内存管理和优化建议
 */
const PerformanceOptimizer = {
    // 性能阈值配置
    thresholds: {
        exportTime: 30000,      // 导出时间阈值：30秒
        memoryUsage: 0.8,       // 内存使用阈值：80%
        cssSize: 1024 * 1024,   // CSS大小阈值：1MB
        imageSize: 5 * 1024 * 1024  // 图片大小阈值：5MB
    },

    // 性能监控数据
    metrics: {
        exportCount: 0,
        totalExportTime: 0,
        averageExportTime: 0,
        memoryPeaks: [],
        errors: []
    },

    /**
     * 开始性能监控
     * @function startMonitoring - 开始监控导出性能
     * @param {string} operation - 操作名称
     * @returns {object} 监控会话对象
     */
    startMonitoring(operation) {
        const session = {
            operation,
            startTime: performance.now(),
            startMemory: this.getMemoryUsage(),
            id: Date.now() + Math.random()
        };

        DebugManager.log('DEBUG', `开始性能监控: ${operation}`, session);
        return session;
    },

    /**
     * 结束性能监控
     * @function endMonitoring - 结束性能监控并记录结果
     * @param {object} session - 监控会话对象
     * @returns {object} 性能报告
     */
    endMonitoring(session) {
        const endTime = performance.now();
        const endMemory = this.getMemoryUsage();
        const duration = endTime - session.startTime;
        const memoryDelta = endMemory.used - session.startMemory.used;

        const report = {
            operation: session.operation,
            duration,
            memoryDelta,
            startMemory: session.startMemory,
            endMemory,
            performance: this.evaluatePerformance(duration, memoryDelta)
        };

        // 更新统计数据
        this.metrics.exportCount++;
        this.metrics.totalExportTime += duration;
        this.metrics.averageExportTime = this.metrics.totalExportTime / this.metrics.exportCount;

        if (endMemory.used > this.metrics.memoryPeaks[this.metrics.memoryPeaks.length - 1]?.used || 0) {
            this.metrics.memoryPeaks.push(endMemory);
        }

        DebugManager.log('INFO', `性能监控完成: ${session.operation}`, report);
        return report;
    },

    /**
     * 获取内存使用情况
     * @function getMemoryUsage - 获取当前内存使用情况
     * @returns {object} 内存使用信息
     */
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return { used: 0, total: 0, limit: 0 };
    },

    /**
     * 评估性能表现
     * @function evaluatePerformance - 评估操作性能
     * @param {number} duration - 耗时（毫秒）
     * @param {number} memoryDelta - 内存变化（MB）
     * @returns {object} 性能评估结果
     */
    evaluatePerformance(duration, memoryDelta) {
        const timeScore = duration < this.thresholds.exportTime ? 'good' : 'poor';
        const memoryScore = Math.abs(memoryDelta) < 50 ? 'good' : 'poor';

        return {
            timeScore,
            memoryScore,
            overall: timeScore === 'good' && memoryScore === 'good' ? 'good' : 'needs_improvement',
            suggestions: this.generateSuggestions(duration, memoryDelta)
        };
    },

    /**
     * 生成优化建议
     * @function generateSuggestions - 根据性能数据生成优化建议
     * @param {number} duration - 耗时
     * @param {number} memoryDelta - 内存变化
     * @returns {array} 建议列表
     */
    generateSuggestions(duration, memoryDelta) {
        const suggestions = [];

        if (duration > this.thresholds.exportTime) {
            suggestions.push('导出时间过长，建议优化DOM结构或减少内容复杂度');
        }

        if (memoryDelta > 100) {
            suggestions.push('内存使用过多，建议清理临时对象或优化图片处理');
        }

        if (this.metrics.exportCount > 10 && this.metrics.averageExportTime > 15000) {
            suggestions.push('平均导出时间较长，建议检查系统性能或浏览器兼容性');
        }

        return suggestions;
    },

    /**
     * 清理内存
     * @function cleanup - 清理不必要的内存占用
     */
    cleanup() {
        try {
            // 清理性能监控历史数据（保留最近10次）
            if (this.metrics.memoryPeaks.length > 10) {
                this.metrics.memoryPeaks = this.metrics.memoryPeaks.slice(-10);
            }

            if (this.metrics.errors.length > 20) {
                this.metrics.errors = this.metrics.errors.slice(-20);
            }

            // 强制垃圾回收（如果可用）
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
            }

            DebugManager.log('DEBUG', '内存清理完成');
        } catch (error) {
            DebugManager.log('WARN', '内存清理失败', error);
        }
    }
};
// #endregion

// #region 错误处理增强
/**
 * 错误处理管理器
 * @description 提供统一的错误处理、恢复机制和用户友好的错误提示
 */
const ErrorHandler = {
    // 错误类型定义
    errorTypes: {
        DEPENDENCY_MISSING: 'dependency_missing',
        CONTAINER_INVALID: 'container_invalid',
        EXPORT_TIMEOUT: 'export_timeout',
        MEMORY_OVERFLOW: 'memory_overflow',
        NETWORK_ERROR: 'network_error',
        UNKNOWN_ERROR: 'unknown_error'
    },

    // 错误恢复策略
    recoveryStrategies: {
        dependency_missing: '请检查html2canvas和jsPDF库是否正确加载',
        container_invalid: '请确保预览内容不为空且DOM结构完整',
        export_timeout: '导出超时，请尝试简化内容或刷新页面重试',
        memory_overflow: '内存不足，请关闭其他标签页或重启浏览器',
        network_error: '网络错误，请检查网络连接',
        unknown_error: '未知错误，请刷新页面重试'
    },

    /**
     * 处理导出错误
     * @function handleExportError - 统一处理导出过程中的错误
     * @param {Error} error - 错误对象
     * @param {string} operation - 操作类型
     * @param {object} context - 错误上下文
     * @returns {object} 错误处理结果
     */
    handleExportError(error, operation, context = {}) {
        const errorInfo = this.analyzeError(error, context);
        const recovery = this.getRecoveryStrategy(errorInfo.type);

        // 记录错误
        PerformanceOptimizer.metrics.errors.push({
            timestamp: new Date().toISOString(),
            operation,
            type: errorInfo.type,
            message: error.message,
            context
        });

        DebugManager.log('ERROR', `导出错误: ${operation}`, {
            错误类型: errorInfo.type,
            错误消息: error.message,
            恢复策略: recovery,
            上下文: context
        });

        // 显示用户友好的错误提示
        this.showUserFriendlyError(errorInfo, recovery);

        return {
            type: errorInfo.type,
            message: error.message,
            recovery,
            canRetry: this.canRetry(errorInfo.type)
        };
    },

    /**
     * 分析错误类型
     * @function analyzeError - 分析错误并确定类型
     * @param {Error} error - 错误对象
     * @param {object} context - 错误上下文
     * @returns {object} 错误分析结果
     */
    analyzeError(error, context) {
        const message = error.message.toLowerCase();

        if (message.includes('html2canvas') || message.includes('jspdf')) {
            return { type: this.errorTypes.DEPENDENCY_MISSING, severity: 'high' };
        }

        if (message.includes('timeout') || message.includes('超时')) {
            return { type: this.errorTypes.EXPORT_TIMEOUT, severity: 'medium' };
        }

        if (message.includes('memory') || message.includes('内存')) {
            return { type: this.errorTypes.MEMORY_OVERFLOW, severity: 'high' };
        }

        if (message.includes('container') || message.includes('容器')) {
            return { type: this.errorTypes.CONTAINER_INVALID, severity: 'medium' };
        }

        if (message.includes('network') || message.includes('网络')) {
            return { type: this.errorTypes.NETWORK_ERROR, severity: 'medium' };
        }

        return { type: this.errorTypes.UNKNOWN_ERROR, severity: 'low' };
    },

    /**
     * 获取恢复策略
     * @function getRecoveryStrategy - 获取错误恢复策略
     * @param {string} errorType - 错误类型
     * @returns {string} 恢复策略描述
     */
    getRecoveryStrategy(errorType) {
        return this.recoveryStrategies[errorType] || this.recoveryStrategies.unknown_error;
    },

    /**
     * 判断是否可以重试
     * @function canRetry - 判断错误是否可以重试
     * @param {string} errorType - 错误类型
     * @returns {boolean} 是否可以重试
     */
    canRetry(errorType) {
        const retryableErrors = [
            this.errorTypes.EXPORT_TIMEOUT,
            this.errorTypes.NETWORK_ERROR,
            this.errorTypes.UNKNOWN_ERROR
        ];
        return retryableErrors.includes(errorType);
    },

    /**
     * 显示用户友好的错误提示
     * @function showUserFriendlyError - 显示用户友好的错误提示
     * @param {object} errorInfo - 错误信息
     * @param {string} recovery - 恢复策略
     */
    showUserFriendlyError(errorInfo, recovery) {
        const title = '导出失败 / Export Failed';
        const message = `${recovery}\n\n如果问题持续存在，请尝试刷新页面或联系技术支持。`;

        // 使用更友好的提示方式
        if (typeof window !== 'undefined' && window.confirm) {
            const retry = window.confirm(`${title}\n\n${message}\n\n是否要重试？`);
            if (retry && this.canRetry(errorInfo.type)) {
                // 可以在这里实现重试逻辑
                DebugManager.log('INFO', '用户选择重试导出');
            }
        } else {
            console.error(`${title}: ${message}`);
        }
    }
};
// #endregion

// #region 模块导出和全局绑定
// 将主要函数绑定到window对象，确保兼容性
if (typeof window !== 'undefined') {
    // 核心导出功能
    window.ModernExportSystem = ModernExportSystem;
    window.ExportConfig = ExportConfig;
    window.DebugManager = DebugManager;

    // 性能和错误处理
    window.PerformanceOptimizer = PerformanceOptimizer;
    window.ErrorHandler = ErrorHandler;

    // 工具函数
    window.generateExportFilename = generateExportFilename;
    window.extractAndEmbedCSS = extractAndEmbedCSS;
    window.optimizeFontRendering = optimizeFontRendering;
    window.preprocessImages = preprocessImages;
    window.reRenderForExport = reRenderForExport;

    // 导出函数
    window.exportToPDF = exportToPDF;
    window.exportToImage = exportToImage;

    // 兼容性函数
    window.exportToPDFCompat = exportToPDFCompat;
    window.exportToImageCompat = exportToImageCompat;

    // 调试工具
    window.debugImageExport = debugImageExport;
    window.testBasicFunctionality = testBasicFunctionality;
    window.checkExternalDependencies = checkExternalDependencies;

    console.log('✅ 导出组件模块已加载完成');
    console.log('📦 可用功能:', {
        '导出系统': 'ModernExportSystem',
        '配置管理': 'ExportConfig',
        '调试工具': 'DebugManager',
        '性能优化': 'PerformanceOptimizer',
        '错误处理': 'ErrorHandler',
        'PDF导出': 'exportToPDF',
        '图片导出': 'exportToImage',
        '调试功能': 'debugImageExport'
    });
}
// #endregion
