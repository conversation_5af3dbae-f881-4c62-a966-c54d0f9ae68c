# 导出组件模块 / Export Components Module

## 项目概述 / Project Overview

这是一个专为发票/收据生成器设计的高质量导出功能模块。该模块从原始的单体HTML文件中提取并重构，采用模块化设计，提供300DPI高质量PDF和图片导出功能。

## 🎯 核心特性 / Core Features

- **高质量导出**: 支持300DPI PDF/PNG/JPEG导出
- **精确尺寸**: A4标准尺寸 (794x1123px)
- **性能优化**: 内存管理、超时保护、性能监控
- **错误处理**: 完善的错误恢复机制
- **样式一致性**: 确保预览与导出100%一致
- **多浏览器兼容**: 支持现代浏览器
- **调试工具**: 完整的调试和诊断功能

## 📁 文件结构 / File Structure

```
export-components/
├── export-components.js              # 主导出模块
├── export-components-documentation.md # 详细文档
├── export-test-cases.js              # 测试用例
├── export-test.html                  # 测试页面
└── README-export-components.md       # 项目说明
```

## 🚀 快速开始 / Quick Start

### 1. 引入依赖库

```html
<!-- 必需的外部库 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<!-- 导出组件模块 -->
<script src="export-components.js"></script>
```

### 2. 初始化系统

```javascript
// 初始化导出系统
ModernExportSystem.init();
```

### 3. 基本使用

```javascript
// PDF导出
await ModernExportSystem.startExport('pdf');

// PNG导出
await ModernExportSystem.startExport('png');

// JPEG导出
await ModernExportSystem.startExport('jpeg');
```

### 4. 事件绑定

```html
<!-- HTML按钮 -->
<button class="export-btn" data-format="pdf">导出PDF</button>
<button class="export-btn" data-format="png">导出PNG</button>

<script>
// 事件委托
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('export-btn')) {
        const format = event.target.dataset.format;
        ModernExportSystem.startExport(format);
    }
});
</script>
```

## 🔧 配置说明 / Configuration

### 基本配置

```javascript
const ExportConfig = {
    // A4尺寸 (794x1123px @ 96DPI)
    a4: { widthPx: 794, heightPx: 1123 },
    
    // 300DPI高质量输出
    dpi: 300,
    
    // 图片尺寸标准
    images: {
        header: { height: 90 },  // 页眉90px
        footer: { height: 70 }   // 页脚70px
    },
    
    // 印章透明度
    stamp: { opacity: 0.9 },
    
    // 性能配置
    timeout: 60000,    // 60秒超时
    maxRetries: 3      // 最大重试3次
};
```

### 自定义配置

```javascript
// 修改超时时间
ExportConfig.timeout = 120000; // 2分钟

// 修改印章透明度
ExportConfig.stamp.opacity = 0.7;

// 启用调试模式
DebugManager.debugMode = true;
```

## 🧪 测试 / Testing

### 运行完整测试

```javascript
// 在浏览器控制台中运行
await runExportTests();
```

### 快速测试

```javascript
// 只运行基础功能测试
runQuickTests();
```

### 测试页面

打开 `export-test.html` 进行交互式测试：

- 基础功能测试
- 依赖库检查
- A4尺寸验证
- 导出质量测试
- 浏览器兼容性测试
- 性能测试

## 📊 性能监控 / Performance Monitoring

### 性能监控

```javascript
// 开始监控
const session = PerformanceOptimizer.startMonitoring('PDF导出');

// 执行导出操作
await exportToPDF(container);

// 结束监控并获取报告
const report = PerformanceOptimizer.endMonitoring(session);
console.log('性能报告:', report);
```

### 内存管理

```javascript
// 检查内存使用
const memory = PerformanceOptimizer.getMemoryUsage();
console.log('内存使用:', memory);

// 清理内存
PerformanceOptimizer.cleanup();
```

## 🛡️ 错误处理 / Error Handling

### 错误类型

- `dependency_missing`: 依赖库未加载
- `container_invalid`: 容器无效或为空
- `export_timeout`: 导出超时
- `memory_overflow`: 内存不足
- `network_error`: 网络错误

### 错误处理示例

```javascript
try {
    await ModernExportSystem.startExport('pdf');
} catch (error) {
    const errorResult = ErrorHandler.handleExportError(error, 'PDF导出');
    
    if (errorResult.canRetry) {
        console.log('可以重试:', errorResult.recovery);
    }
}
```

## 🔍 调试工具 / Debugging Tools

### 基础调试

```javascript
// 调试导出功能
debugImageExport();

// 测试基础功能
testBasicFunctionality();

// 检查依赖
checkExternalDependencies();
```

### 日志记录

```javascript
// 记录日志
DebugManager.log('INFO', '操作完成', { data: 'example' });

// 性能监控
DebugManager.startPerformanceMetric('操作名称');
const duration = DebugManager.endPerformanceMetric('操作名称');
```

## 🌐 浏览器兼容性 / Browser Compatibility

### 支持的浏览器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 依赖库版本
- html2canvas: 1.4.1+
- jsPDF: 2.5.1+

## 📋 API 参考 / API Reference

### 主要类和方法

#### ModernExportSystem
- `init()`: 初始化导出系统
- `startExport(format)`: 开始导出
- `preExportValidation(format)`: 预导出验证

#### PerformanceOptimizer
- `startMonitoring(operation)`: 开始性能监控
- `endMonitoring(session)`: 结束性能监控
- `getMemoryUsage()`: 获取内存使用情况
- `cleanup()`: 清理内存

#### ErrorHandler
- `handleExportError(error, operation, context)`: 处理导出错误
- `analyzeError(error, context)`: 分析错误类型
- `canRetry(errorType)`: 判断是否可重试

### 工具函数

- `generateExportFilename(format, data)`: 生成文件名
- `extractAndEmbedCSS()`: 提取CSS样式
- `optimizeFontRendering(container)`: 优化字体渲染
- `preprocessImages(container)`: 预处理图片
- `exportToPDF(container)`: 直接PDF导出
- `exportToImage(container, format)`: 直接图片导出

## 🔧 故障排除 / Troubleshooting

### 常见问题

1. **导出失败**
   - 检查依赖库是否加载
   - 确认容器内容不为空
   - 查看控制台错误信息

2. **导出内容不完整**
   - 确保图片已加载完成
   - 检查CSS样式应用
   - 验证容器尺寸

3. **性能问题**
   - 减少内容复杂度
   - 优化图片大小
   - 定期清理内存

### 调试步骤

1. 运行 `debugImageExport()` 获取诊断信息
2. 检查 `checkExternalDependencies()` 结果
3. 运行 `testBasicFunctionality()` 验证功能
4. 查看浏览器控制台错误信息

## 📝 更新日志 / Changelog

### v1.0.0 (2024-12-21)
- ✨ 初始版本发布
- 🎯 支持PDF/PNG/JPEG高质量导出
- ⚡ 集成性能优化和错误处理
- 🔍 完整的调试和测试工具
- 📚 详细的文档和示例

## 🤝 贡献 / Contributing

欢迎提交问题报告和功能请求。在提交代码前，请确保：

1. 运行所有测试用例
2. 更新相关文档
3. 遵循代码规范

## 📄 许可证 / License

MIT License - 详见 LICENSE 文件

## 👥 作者 / Authors

SmartOffice Team

## 🙏 致谢 / Acknowledgments

- html2canvas 团队提供的优秀Canvas渲染库
- jsPDF 团队提供的PDF生成库
- 所有测试和反馈的用户
