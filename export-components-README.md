# 导出组件模块 - Export Components Module

## 概述

`export-components.js` 是发票/收据生成器的独立导出功能模块，提供300DPI高质量PDF和图片导出功能。

## 主要功能

- ✅ **300DPI高质量导出** - PDF/PNG/JPEG格式
- ✅ **A4尺寸精确控制** - 794x1123px标准尺寸
- ✅ **页眉/页脚图片处理** - 90px/70px固定高度
- ✅ **印章透明度控制** - 0.9透明度设置
- ✅ **多订单支持** - 斜杠分隔格式(order1/order2/order3)
- ✅ **样式一致性保证** - 预览与导出100%一致
- ✅ **完整调试工具** - 故障排除和性能监控

## 模块结构

### 核心组件

1. **ExportConfig** - 导出配置管理
2. **DebugManager** - 调试和日志管理
3. **ModernExportSystem** - 导出系统核心
4. **工具函数集** - 文件名生成、CSS提取、字体优化等
5. **导出功能** - PDF/图片导出实现
6. **调试工具** - 诊断和测试功能

### 配置参数

```javascript
const ExportConfig = {
    a4: {
        widthPx: 794,    // A4宽度(px)
        heightPx: 1123   // A4高度(px)
    },
    dpi: 300,           // 输出分辨率
    quality: {
        scale: 3.125,   // 缩放比例
        pngQuality: 1.0,
        jpegQuality: 0.95
    },
    images: {
        header: { height: 90 }, // 页眉高度
        footer: { height: 70 }  // 页脚高度
    },
    stamp: {
        opacity: 0.9    // 印章透明度
    },
    margins: {
        left: 10,       // 左边距
        right: 10       // 右边距
    }
};
```

## 依赖关系

### 外部库依赖

- **html2canvas** (v1.4.1+) - Canvas渲染
- **jsPDF** (v2.5.1+) - PDF生成

### 主HTML文件依赖

模块需要以下外部函数（由主HTML文件提供）：

```javascript
// 必需函数
collectFormData()           // 收集表单数据
InvoiceTemplate.render()    // 发票模板渲染
ReceiptTemplate.render()    // 收据模板渲染

// 可选函数
updatePreview()            // 更新预览
refreshDOMCache()          // 刷新DOM缓存
PreviewManager.isUpdating  // 预览状态检查
```

### DOM元素依赖

```javascript
// 必需元素
#document-container        // 主文档容器
.preview-section          // 预览区域
.btn-group               // 按钮组容器

// 可选元素
#document-preview         // 预览容器
.document-header-image-container  // 页眉图片容器
.company-footer-image-container   // 页脚图片容器
.company-stamp           // 印章容器
```

## 使用方法

### 1. 引入模块

```html
<!-- 在主HTML文件中引入 -->
<script src="export-components.js"></script>
```

### 2. 初始化系统

```javascript
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化导出系统
    ModernExportSystem.init();
    
    // 初始化调试管理器
    DebugManager.init();
});
```

### 3. 导出功能调用

```javascript
// PDF导出
await ModernExportSystem.startExport('pdf');

// PNG导出
await ModernExportSystem.startExport('png');

// JPEG导出
await ModernExportSystem.startExport('jpeg');

// 或使用兼容性函数
await exportToPDFCompat();
await exportToImageCompat('png');
```

### 4. 调试工具

```javascript
// 调试导出功能
debugImageExport();

// 测试基础功能
testBasicFunctionality();

// 检查外部依赖
checkExternalDependencies();
```

## API接口

### ModernExportSystem

| 方法 | 描述 | 参数 |
|------|------|------|
| `init()` | 初始化导出系统 | 无 |
| `startExport(format)` | 开始导出流程 | format: 'pdf'/'png'/'jpeg' |
| `createExportUI()` | 创建导出界面 | 无 |
| `bindEvents()` | 绑定事件处理 | 无 |

### 工具函数

| 函数 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `generateExportFilename(format, data)` | 生成文件名 | format, data | string |
| `extractAndEmbedCSS()` | 提取CSS样式 | 无 | string |
| `optimizeFontRendering(container)` | 优化字体渲染 | HTMLElement | void |
| `preprocessImages(container)` | 预处理图片 | HTMLElement | Promise |

### 调试工具

| 函数 | 描述 | 返回值 |
|------|------|--------|
| `debugImageExport()` | 调试导出功能 | object |
| `testBasicFunctionality()` | 测试基础功能 | object |
| `checkExternalDependencies()` | 检查依赖库 | object |

## 错误处理

模块包含完整的错误处理机制：

- **依赖检查** - 自动检测html2canvas和jsPDF
- **容器验证** - 确保必需DOM元素存在
- **内容验证** - 验证预览内容与表单数据一致性
- **超时保护** - 防止html2canvas无限等待
- **优雅降级** - 导出失败时的用户友好提示

## 性能优化

- **按需加载** - 只在需要时初始化
- **缓存机制** - CSS样式和图片缓存
- **性能监控** - 内置性能指标收集
- **内存管理** - 及时清理临时DOM元素

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 更新日志

### v3.0 (2024-12-21)
- ✅ 独立模块化架构
- ✅ 印章透明度调整为0.9
- ✅ 添加10px左右边距
- ✅ 完善多订单支持
- ✅ 增强调试工具
- ✅ 优化性能监控

## 故障排除

### 常见问题

1. **导出内容为空**
   - 检查`collectFormData()`函数是否可用
   - 确认预览容器有内容

2. **图片不显示**
   - 检查图片路径是否正确
   - 确认图片已完全加载

3. **样式丢失**
   - 检查CSS样式是否正确提取
   - 确认导出模式样式应用

4. **字体显示异常**
   - 等待字体加载完成
   - 检查字体fallback设置

### 调试步骤

1. 调用`debugImageExport()`查看详细信息
2. 检查浏览器控制台错误信息
3. 验证外部依赖库加载状态
4. 确认DOM结构完整性

## 技术支持

如遇问题，请提供：
- 浏览器版本信息
- 错误控制台输出
- `debugImageExport()`结果
- 复现步骤描述
