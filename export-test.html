<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试 - Export Function Test</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .test-results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        #test-document {
            width: 794px;
            min-height: 1123px;
            background: white;
            border: 1px solid #ccc;
            margin: 20px auto;
            padding: 40px;
            box-sizing: border-box;
        }
        .test-content {
            font-size: 14px;
            line-height: 1.6;
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #333;
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .test-table th, .test-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .test-table th {
            background-color: #f2f2f2;
        }
        .test-footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>导出功能质量验证测试 / Export Quality Validation Test</h1>
        
        <div class="test-grid">
            <!-- 基础功能测试 -->
            <div class="test-section">
                <h3>🔧 基础功能测试 / Basic Function Test</h3>
                <button onclick="runBasicTests()">运行基础测试 / Run Basic Tests</button>
                <div id="basic-test-results" class="test-results"></div>
            </div>

            <!-- 依赖检查测试 -->
            <div class="test-section">
                <h3>📦 依赖库检查 / Dependencies Check</h3>
                <button onclick="runDependencyTests()">检查依赖 / Check Dependencies</button>
                <div id="dependency-test-results" class="test-results"></div>
            </div>

            <!-- 尺寸精确性测试 -->
            <div class="test-section">
                <h3>📏 A4尺寸测试 / A4 Size Test</h3>
                <button onclick="runSizeTests()">测试尺寸 / Test Dimensions</button>
                <div id="size-test-results" class="test-results"></div>
            </div>

            <!-- 导出质量测试 -->
            <div class="test-section">
                <h3>🎨 导出质量测试 / Export Quality Test</h3>
                <button onclick="runQualityTests()">测试质量 / Test Quality</button>
                <div id="quality-test-results" class="test-results"></div>
            </div>

            <!-- 浏览器兼容性测试 -->
            <div class="test-section">
                <h3>🌐 浏览器兼容性 / Browser Compatibility</h3>
                <button onclick="runCompatibilityTests()">测试兼容性 / Test Compatibility</button>
                <div id="compatibility-test-results" class="test-results"></div>
            </div>

            <!-- 性能测试 -->
            <div class="test-section">
                <h3>⚡ 性能测试 / Performance Test</h3>
                <button onclick="runPerformanceTests()">测试性能 / Test Performance</button>
                <div id="performance-test-results" class="test-results"></div>
            </div>
        </div>

        <!-- 测试文档容器 -->
        <div class="test-section">
            <h3>📄 测试文档 / Test Document</h3>
            <div id="test-document">
                <div class="test-content">
                    <div class="test-header">
                        <h2>测试发票 / Test Invoice</h2>
                        <p>Invoice #: TEST-2024-001</p>
                        <p>Date: 2024-12-21</p>
                    </div>
                    
                    <div style="margin: 20px 0;">
                        <strong>客户信息 / Customer Information:</strong><br>
                        测试客户 / Test Customer<br>
                        123 Test Street, Test City<br>
                        电话 / Phone: +86 123-4567-8900
                    </div>

                    <table class="test-table">
                        <thead>
                            <tr>
                                <th>项目 / Item</th>
                                <th>数量 / Qty</th>
                                <th>单价 / Price</th>
                                <th>金额 / Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>测试服务 / Test Service</td>
                                <td>1</td>
                                <td>¥100.00</td>
                                <td>¥100.00</td>
                            </tr>
                            <tr>
                                <td>额外费用 / Additional Fee</td>
                                <td>2</td>
                                <td>¥50.00</td>
                                <td>¥100.00</td>
                            </tr>
                        </tbody>
                    </table>

                    <div style="text-align: right; margin: 20px 0;">
                        <strong>总计 / Total: ¥200.00</strong>
                    </div>

                    <div class="test-footer">
                        <p>此为测试文档，用于验证导出功能</p>
                        <p>This is a test document for export function validation</p>
                        <p>生成时间 / Generated: <span id="generation-time"></span></p>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button onclick="testExportPDF()">测试PDF导出 / Test PDF Export</button>
                <button onclick="testExportPNG()">测试PNG导出 / Test PNG Export</button>
                <button onclick="testExportJPEG()">测试JPEG导出 / Test JPEG Export</button>
            </div>
        </div>

        <!-- 综合测试报告 -->
        <div class="test-section">
            <h3>📊 综合测试报告 / Comprehensive Test Report</h3>
            <button onclick="generateTestReport()">生成报告 / Generate Report</button>
            <div id="test-report" class="test-results"></div>
        </div>
    </div>

    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <!-- 引入导出组件模块 -->
    <script src="export-components.js"></script>

    <script>
        // 设置生成时间
        document.getElementById('generation-time').textContent = new Date().toLocaleString();

        // 测试结果存储
        const testResults = {
            basic: null,
            dependency: null,
            size: null,
            quality: null,
            compatibility: null,
            performance: null
        };

        /**
         * 运行基础功能测试
         */
        function runBasicTests() {
            const results = document.getElementById('basic-test-results');
            results.innerHTML = '<p class="info">正在运行基础功能测试...</p>';

            try {
                const tests = [];

                // 测试导出配置是否存在
                tests.push({
                    name: 'ExportConfig 配置检查',
                    result: typeof ExportConfig !== 'undefined',
                    details: typeof ExportConfig !== 'undefined' ?
                        `A4尺寸: ${ExportConfig.a4.widthPx}x${ExportConfig.a4.heightPx}px, DPI: ${ExportConfig.dpi}` :
                        '配置对象不存在'
                });

                // 测试调试管理器
                tests.push({
                    name: 'DebugManager 调试管理器',
                    result: typeof DebugManager !== 'undefined' && typeof DebugManager.log === 'function',
                    details: typeof DebugManager !== 'undefined' ? '调试管理器可用' : '调试管理器不存在'
                });

                // 测试ModernExportSystem
                tests.push({
                    name: 'ModernExportSystem 导出系统',
                    result: typeof ModernExportSystem !== 'undefined' && typeof ModernExportSystem.startExport === 'function',
                    details: typeof ModernExportSystem !== 'undefined' ? '导出系统可用' : '导出系统不存在'
                });

                // 测试工具函数
                const toolFunctions = ['generateExportFilename', 'extractAndEmbedCSS', 'optimizeFontRendering', 'preprocessImages'];
                toolFunctions.forEach(funcName => {
                    tests.push({
                        name: `${funcName} 工具函数`,
                        result: typeof window[funcName] === 'function',
                        details: typeof window[funcName] === 'function' ? '函数可用' : '函数不存在'
                    });
                });

                // 测试导出函数
                const exportFunctions = ['exportToPDF', 'exportToImage', 'debugImageExport'];
                exportFunctions.forEach(funcName => {
                    tests.push({
                        name: `${funcName} 导出函数`,
                        result: typeof window[funcName] === 'function',
                        details: typeof window[funcName] === 'function' ? '函数可用' : '函数不存在'
                    });
                });

                // 生成测试结果
                const passedTests = tests.filter(t => t.result).length;
                const totalTests = tests.length;

                testResults.basic = {
                    passed: passedTests,
                    total: totalTests,
                    success: passedTests === totalTests,
                    details: tests
                };

                let html = `<h4>基础功能测试结果: ${passedTests}/${totalTests} 通过</h4>`;
                tests.forEach(test => {
                    const status = test.result ? 'success' : 'error';
                    const icon = test.result ? '✅' : '❌';
                    html += `<p class="${status}">${icon} ${test.name}: ${test.details}</p>`;
                });

                results.innerHTML = html;

            } catch (error) {
                results.innerHTML = `<p class="error">❌ 基础功能测试失败: ${error.message}</p>`;
                testResults.basic = { success: false, error: error.message };
            }
        }

        /**
         * 运行依赖库检查测试
         */
        function runDependencyTests() {
            const results = document.getElementById('dependency-test-results');
            results.innerHTML = '<p class="info">正在检查依赖库...</p>';

            try {
                const dependencies = [
                    {
                        name: 'html2canvas',
                        check: () => typeof html2canvas !== 'undefined',
                        version: () => html2canvas.version || '未知版本',
                        required: true
                    },
                    {
                        name: 'jsPDF',
                        check: () => typeof window.jspdf !== 'undefined',
                        version: () => window.jspdf.jsPDF ? 'v2.x' : '未知版本',
                        required: true
                    }
                ];

                let html = '<h4>依赖库检查结果:</h4>';
                let allPassed = true;

                dependencies.forEach(dep => {
                    const available = dep.check();
                    const status = available ? 'success' : 'error';
                    const icon = available ? '✅' : '❌';
                    const version = available ? dep.version() : 'N/A';

                    html += `<p class="${status}">${icon} ${dep.name}: ${available ? '可用' : '不可用'} (${version})</p>`;

                    if (dep.required && !available) {
                        allPassed = false;
                    }
                });

                testResults.dependency = {
                    success: allPassed,
                    details: dependencies.map(dep => ({
                        name: dep.name,
                        available: dep.check(),
                        version: dep.check() ? dep.version() : null
                    }))
                };

                results.innerHTML = html;

            } catch (error) {
                results.innerHTML = `<p class="error">❌ 依赖检查失败: ${error.message}</p>`;
                testResults.dependency = { success: false, error: error.message };
            }
        }

        /**
         * 运行A4尺寸测试
         */
        function runSizeTests() {
            const results = document.getElementById('size-test-results');
            results.innerHTML = '<p class="info">正在测试A4尺寸精确性...</p>';

            try {
                const testDoc = document.getElementById('test-document');
                const rect = testDoc.getBoundingClientRect();

                const expectedWidth = 794;
                const expectedHeight = 1123;
                const tolerance = 2; // 允许2px误差

                const widthMatch = Math.abs(rect.width - expectedWidth) <= tolerance;
                const heightMatch = Math.abs(rect.height - expectedHeight) <= tolerance;

                const tests = [
                    {
                        name: 'A4宽度精确性',
                        expected: expectedWidth,
                        actual: Math.round(rect.width),
                        passed: widthMatch,
                        tolerance: tolerance
                    },
                    {
                        name: 'A4高度精确性',
                        expected: expectedHeight,
                        actual: Math.round(rect.height),
                        passed: heightMatch,
                        tolerance: tolerance
                    }
                ];

                const allPassed = tests.every(t => t.passed);
                testResults.size = {
                    success: allPassed,
                    details: tests
                };

                let html = `<h4>A4尺寸测试结果:</h4>`;
                tests.forEach(test => {
                    const status = test.passed ? 'success' : 'error';
                    const icon = test.passed ? '✅' : '❌';
                    const diff = Math.abs(test.actual - test.expected);
                    html += `<p class="${status}">${icon} ${test.name}: ${test.actual}px (期望: ${test.expected}px, 误差: ${diff}px)</p>`;
                });

                if (typeof ExportConfig !== 'undefined') {
                    html += `<p class="info">📋 配置信息: ${ExportConfig.a4.widthPx}x${ExportConfig.a4.heightPx}px @ ${ExportConfig.dpi}DPI</p>`;
                }

                results.innerHTML = html;

            } catch (error) {
                results.innerHTML = `<p class="error">❌ 尺寸测试失败: ${error.message}</p>`;
                testResults.size = { success: false, error: error.message };
            }
        }

        /**
         * 运行导出质量测试
         */
        function runQualityTests() {
            const results = document.getElementById('quality-test-results');
            results.innerHTML = '<p class="info">正在测试导出质量...</p>';

            try {
                const tests = [];

                // 测试CSS提取功能
                if (typeof extractAndEmbedCSS === 'function') {
                    const css = extractAndEmbedCSS();
                    tests.push({
                        name: 'CSS样式提取',
                        passed: css.length > 0,
                        details: `提取了 ${css.length} 字符的CSS样式`
                    });
                } else {
                    tests.push({
                        name: 'CSS样式提取',
                        passed: false,
                        details: 'extractAndEmbedCSS 函数不可用'
                    });
                }

                // 测试文件名生成
                if (typeof generateExportFilename === 'function') {
                    const filename = generateExportFilename('pdf', {
                        documentType: 'invoice',
                        documentNumber: 'TEST-001'
                    });
                    tests.push({
                        name: '文件名生成',
                        passed: filename.includes('TEST-001') && filename.includes('300DPI'),
                        details: `生成文件名: ${filename}`
                    });
                } else {
                    tests.push({
                        name: '文件名生成',
                        passed: false,
                        details: 'generateExportFilename 函数不可用'
                    });
                }

                // 测试配置完整性
                if (typeof ExportConfig !== 'undefined') {
                    const configTests = [
                        { key: 'a4.widthPx', expected: 794 },
                        { key: 'a4.heightPx', expected: 1123 },
                        { key: 'dpi', expected: 300 },
                        { key: 'quality.scale', expected: 3.125 },
                        { key: 'stamp.opacity', expected: 0.9 }
                    ];

                    configTests.forEach(test => {
                        const value = test.key.split('.').reduce((obj, key) => obj?.[key], ExportConfig);
                        tests.push({
                            name: `配置项 ${test.key}`,
                            passed: value === test.expected,
                            details: `值: ${value} (期望: ${test.expected})`
                        });
                    });
                }

                const passedTests = tests.filter(t => t.passed).length;
                const totalTests = tests.length;

                testResults.quality = {
                    passed: passedTests,
                    total: totalTests,
                    success: passedTests === totalTests,
                    details: tests
                };

                let html = `<h4>导出质量测试结果: ${passedTests}/${totalTests} 通过</h4>`;
                tests.forEach(test => {
                    const status = test.passed ? 'success' : 'error';
                    const icon = test.passed ? '✅' : '❌';
                    html += `<p class="${status}">${icon} ${test.name}: ${test.details}</p>`;
                });

                results.innerHTML = html;

            } catch (error) {
                results.innerHTML = `<p class="error">❌ 质量测试失败: ${error.message}</p>`;
                testResults.quality = { success: false, error: error.message };
            }
        }

        /**
         * 运行浏览器兼容性测试
         */
        function runCompatibilityTests() {
            const results = document.getElementById('compatibility-test-results');
            results.innerHTML = '<p class="info">正在测试浏览器兼容性...</p>';

            try {
                const browserInfo = {
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    language: navigator.language,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine
                };

                // 检测浏览器类型
                const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
                const isFirefox = /Firefox/.test(navigator.userAgent);
                const isSafari = /Safari/.test(navigator.userAgent) && /Apple Computer/.test(navigator.vendor);
                const isEdge = /Edg/.test(navigator.userAgent);

                const browserTests = [
                    {
                        name: 'Canvas支持',
                        passed: !!document.createElement('canvas').getContext,
                        details: document.createElement('canvas').getContext ? '支持Canvas API' : '不支持Canvas API'
                    },
                    {
                        name: 'Blob支持',
                        passed: typeof Blob !== 'undefined',
                        details: typeof Blob !== 'undefined' ? '支持Blob API' : '不支持Blob API'
                    },
                    {
                        name: 'URL.createObjectURL支持',
                        passed: typeof URL !== 'undefined' && typeof URL.createObjectURL === 'function',
                        details: typeof URL !== 'undefined' && typeof URL.createObjectURL === 'function' ?
                            '支持URL API' : '不支持URL API'
                    },
                    {
                        name: 'Promise支持',
                        passed: typeof Promise !== 'undefined',
                        details: typeof Promise !== 'undefined' ? '支持Promise' : '不支持Promise'
                    },
                    {
                        name: 'async/await支持',
                        passed: (function() {
                            try {
                                eval('(async function() {})');
                                return true;
                            } catch (e) {
                                return false;
                            }
                        })(),
                        details: '检测async/await语法支持'
                    }
                ];

                const passedTests = browserTests.filter(t => t.passed).length;
                const totalTests = browserTests.length;

                testResults.compatibility = {
                    passed: passedTests,
                    total: totalTests,
                    success: passedTests === totalTests,
                    browserInfo: browserInfo,
                    details: browserTests
                };

                let html = `<h4>浏览器兼容性测试结果: ${passedTests}/${totalTests} 通过</h4>`;
                html += `<p class="info">🌐 浏览器: ${navigator.userAgent.split(' ')[0]} on ${navigator.platform}</p>`;

                browserTests.forEach(test => {
                    const status = test.passed ? 'success' : 'error';
                    const icon = test.passed ? '✅' : '❌';
                    html += `<p class="${status}">${icon} ${test.name}: ${test.details}</p>`;
                });

                results.innerHTML = html;

            } catch (error) {
                results.innerHTML = `<p class="error">❌ 兼容性测试失败: ${error.message}</p>`;
                testResults.compatibility = { success: false, error: error.message };
            }
        }

        /**
         * 运行性能测试
         */
        function runPerformanceTests() {
            const results = document.getElementById('performance-test-results');
            results.innerHTML = '<p class="info">正在测试性能...</p>';

            try {
                const performanceTests = [];

                // 测试CSS提取性能
                if (typeof extractAndEmbedCSS === 'function') {
                    const start = performance.now();
                    extractAndEmbedCSS();
                    const duration = performance.now() - start;

                    performanceTests.push({
                        name: 'CSS提取性能',
                        duration: duration,
                        passed: duration < 1000, // 应该在1秒内完成
                        details: `耗时: ${duration.toFixed(2)}ms`
                    });
                }

                // 测试DOM操作性能
                const domStart = performance.now();
                const testElement = document.createElement('div');
                testElement.innerHTML = '<p>性能测试</p>'.repeat(100);
                document.body.appendChild(testElement);
                document.body.removeChild(testElement);
                const domDuration = performance.now() - domStart;

                performanceTests.push({
                    name: 'DOM操作性能',
                    duration: domDuration,
                    passed: domDuration < 100,
                    details: `耗时: ${domDuration.toFixed(2)}ms`
                });

                // 测试内存使用情况
                if (performance.memory) {
                    const memoryInfo = {
                        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                    };

                    performanceTests.push({
                        name: '内存使用情况',
                        duration: 0,
                        passed: memoryInfo.used < memoryInfo.limit * 0.8,
                        details: `已用: ${memoryInfo.used}MB / 总计: ${memoryInfo.total}MB / 限制: ${memoryInfo.limit}MB`
                    });
                }

                const passedTests = performanceTests.filter(t => t.passed).length;
                const totalTests = performanceTests.length;

                testResults.performance = {
                    passed: passedTests,
                    total: totalTests,
                    success: passedTests === totalTests,
                    details: performanceTests
                };

                let html = `<h4>性能测试结果: ${passedTests}/${totalTests} 通过</h4>`;
                performanceTests.forEach(test => {
                    const status = test.passed ? 'success' : 'warning';
                    const icon = test.passed ? '✅' : '⚠️';
                    html += `<p class="${status}">${icon} ${test.name}: ${test.details}</p>`;
                });

                results.innerHTML = html;

            } catch (error) {
                results.innerHTML = `<p class="error">❌ 性能测试失败: ${error.message}</p>`;
                testResults.performance = { success: false, error: error.message };
            }
        }

        /**
         * 测试PDF导出
         */
        async function testExportPDF() {
            try {
                const container = document.getElementById('test-document');
                if (typeof exportToPDF === 'function') {
                    await exportToPDF(container);
                    alert('✅ PDF导出测试完成！请检查下载的文件。');
                } else {
                    alert('❌ exportToPDF 函数不可用');
                }
            } catch (error) {
                alert(`❌ PDF导出测试失败: ${error.message}`);
            }
        }

        /**
         * 测试PNG导出
         */
        async function testExportPNG() {
            try {
                const container = document.getElementById('test-document');
                if (typeof exportToImage === 'function') {
                    await exportToImage(container, 'png');
                    alert('✅ PNG导出测试完成！请检查下载的文件。');
                } else {
                    alert('❌ exportToImage 函数不可用');
                }
            } catch (error) {
                alert(`❌ PNG导出测试失败: ${error.message}`);
            }
        }

        /**
         * 测试JPEG导出
         */
        async function testExportJPEG() {
            try {
                const container = document.getElementById('test-document');
                if (typeof exportToImage === 'function') {
                    await exportToImage(container, 'jpeg');
                    alert('✅ JPEG导出测试完成！请检查下载的文件。');
                } else {
                    alert('❌ exportToImage 函数不可用');
                }
            } catch (error) {
                alert(`❌ JPEG导出测试失败: ${error.message}`);
            }
        }

        /**
         * 生成综合测试报告
         */
        function generateTestReport() {
            const reportDiv = document.getElementById('test-report');

            // 检查是否所有测试都已运行
            const unrunTests = Object.keys(testResults).filter(key => testResults[key] === null);

            if (unrunTests.length > 0) {
                reportDiv.innerHTML = `<p class="warning">⚠️ 请先运行所有测试: ${unrunTests.join(', ')}</p>`;
                return;
            }

            // 计算总体通过率
            const totalTests = Object.values(testResults).reduce((sum, result) => {
                return sum + (result.total || 1);
            }, 0);

            const passedTests = Object.values(testResults).reduce((sum, result) => {
                return sum + (result.passed || (result.success ? 1 : 0));
            }, 0);

            const overallSuccess = Object.values(testResults).every(result => result.success);
            const passRate = ((passedTests / totalTests) * 100).toFixed(1);

            // 生成报告
            let html = `
                <h4>📊 综合测试报告</h4>
                <div style="background: ${overallSuccess ? '#d4edda' : '#f8d7da'}; padding: 15px; border-radius: 5px; margin: 10px 0;">
                    <h5>总体结果: ${overallSuccess ? '✅ 通过' : '❌ 失败'}</h5>
                    <p>通过率: ${passedTests}/${totalTests} (${passRate}%)</p>
                    <p>测试时间: ${new Date().toLocaleString()}</p>
                </div>
            `;

            // 详细结果
            html += '<h5>详细结果:</h5>';
            Object.entries(testResults).forEach(([testName, result]) => {
                const status = result.success ? 'success' : 'error';
                const icon = result.success ? '✅' : '❌';
                const details = result.error || `${result.passed || 0}/${result.total || 1} 通过`;
                html += `<p class="${status}">${icon} ${testName}: ${details}</p>`;
            });

            // 建议
            html += '<h5>建议:</h5>';
            if (overallSuccess) {
                html += '<p class="success">✅ 所有测试通过，导出功能工作正常！</p>';
            } else {
                html += '<p class="error">❌ 部分测试失败，请检查相关功能。</p>';

                Object.entries(testResults).forEach(([testName, result]) => {
                    if (!result.success) {
                        html += `<p class="warning">⚠️ ${testName} 测试失败，请检查相关配置和依赖。</p>`;
                    }
                });
            }

            reportDiv.innerHTML = html;
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('🔧 导出功能测试页面已加载');
                if (typeof DebugManager !== 'undefined') {
                    DebugManager.log('INFO', '测试页面初始化完成');
                }
            }, 1000);
        });
    </script>
</body>
</html>
