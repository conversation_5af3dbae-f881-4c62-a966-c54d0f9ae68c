# 导出组件模块文档 / Export Components Module Documentation

## 概述 / Overview

`export-components.js` 是一个独立的导出功能模块，专门为发票/收据生成器提供高质量的PDF和图片导出功能。该模块采用模块化设计，支持300DPI高质量输出、多种格式导出、性能优化和完善的错误处理。

## 核心特性 / Core Features

- 🎯 **高质量导出**: 支持300DPI PDF/PNG/JPEG导出
- 📏 **精确尺寸**: A4标准尺寸 (794x1123px)
- 🔧 **性能优化**: 内存管理、超时保护、性能监控
- 🛡️ **错误处理**: 完善的错误恢复机制和用户友好提示
- 🎨 **样式一致性**: 确保预览与导出100%一致
- 📱 **多浏览器兼容**: 支持现代浏览器
- 🔍 **调试工具**: 完整的调试和诊断功能

## 模块结构 / Module Structure

### 1. 配置管理 (ExportConfig)
```javascript
const ExportConfig = {
    // A4尺寸配置
    a4: {
        width: 210,      // A4宽度 (mm)
        height: 297,     // A4高度 (mm)
        widthPx: 794,    // A4宽度 (px, 96DPI)
        heightPx: 1123   // A4高度 (px, 96DPI)
    },
    
    // DPI设置
    dpi: 300,
    
    // 图片尺寸标准
    images: {
        header: { height: 90 },  // 页眉高度90px
        footer: { height: 70 },  // 页脚高度70px
        stamp: { width: 120, height: 120 }
    },
    
    // 印章配置
    stamp: {
        opacity: 0.9,    // 透明度0.9
        width: 120,
        height: 120
    },
    
    // 性能配置
    timeout: 60000,      // 60秒超时
    maxRetries: 3,       // 最大重试3次
    
    // 质量配置
    quality: {
        scale: 3.125,    // 缩放比例
        jpegQuality: 0.95
    }
};
```

### 2. 调试管理器 (DebugManager)
```javascript
// 日志记录
DebugManager.log('INFO', '消息', 数据对象);

// 性能监控
DebugManager.startPerformanceMetric('操作名称');
const duration = DebugManager.endPerformanceMetric('操作名称');
```

### 3. 性能优化器 (PerformanceOptimizer)
```javascript
// 开始监控
const session = PerformanceOptimizer.startMonitoring('PDF导出');

// 结束监控
const report = PerformanceOptimizer.endMonitoring(session);

// 内存清理
PerformanceOptimizer.cleanup();
```

### 4. 错误处理器 (ErrorHandler)
```javascript
// 处理导出错误
const result = ErrorHandler.handleExportError(error, '操作类型', 上下文);
```

## 主要功能 / Main Functions

### 1. 导出系统 (ModernExportSystem)

#### 初始化
```javascript
ModernExportSystem.init();
```

#### 开始导出
```javascript
// PDF导出
await ModernExportSystem.startExport('pdf');

// PNG导出
await ModernExportSystem.startExport('png');

// JPEG导出
await ModernExportSystem.startExport('jpeg');
```

### 2. 直接导出函数

#### PDF导出
```javascript
const container = document.getElementById('document-container');
await exportToPDF(container);
```

#### 图片导出
```javascript
const container = document.getElementById('document-container');
await exportToImage(container, 'png');  // 或 'jpeg'
```

### 3. 工具函数

#### 文件名生成
```javascript
const filename = generateExportFilename('pdf', formData);
// 输出: "发票_Invoice_INV-001_300DPI.pdf"
```

#### CSS样式提取
```javascript
const cssText = extractAndEmbedCSS();
```

#### 字体优化
```javascript
optimizeFontRendering(container);
```

#### 图片预处理
```javascript
await preprocessImages(container);
```

## 使用指南 / Usage Guide

### 1. 基本使用

```html
<!-- 引入依赖库 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<!-- 引入导出组件 -->
<script src="export-components.js"></script>

<script>
// 初始化导出系统
ModernExportSystem.init();

// 导出PDF
async function exportPDF() {
    try {
        await ModernExportSystem.startExport('pdf');
        console.log('PDF导出成功');
    } catch (error) {
        console.error('PDF导出失败:', error);
    }
}
</script>
```

### 2. 事件绑定

```javascript
// 使用事件委托
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('export-btn')) {
        const format = event.target.dataset.format;
        if (format && typeof ModernExportSystem !== 'undefined') {
            ModernExportSystem.startExport(format);
        }
    }
});
```

### 3. HTML按钮示例

```html
<button class="export-btn" data-format="pdf">导出PDF</button>
<button class="export-btn" data-format="png">导出PNG</button>
<button class="export-btn" data-format="jpeg">导出JPEG</button>
```

## 配置说明 / Configuration

### 1. 自定义配置

```javascript
// 修改超时时间
ExportConfig.timeout = 120000; // 2分钟

// 修改DPI设置
ExportConfig.dpi = 150;

// 修改印章透明度
ExportConfig.stamp.opacity = 0.7;
```

### 2. 调试模式

```javascript
// 启用/禁用调试
DebugManager.debugMode = true;

// 设置日志级别
DebugManager.currentLogLevel = 3; // DEBUG级别
```

## 错误处理 / Error Handling

### 1. 常见错误类型

- `dependency_missing`: 依赖库未加载
- `container_invalid`: 容器无效或为空
- `export_timeout`: 导出超时
- `memory_overflow`: 内存不足
- `network_error`: 网络错误

### 2. 错误恢复策略

```javascript
try {
    await ModernExportSystem.startExport('pdf');
} catch (error) {
    const errorResult = ErrorHandler.handleExportError(error, 'PDF导出');
    
    if (errorResult.canRetry) {
        // 可以重试
        console.log('错误可重试:', errorResult.recovery);
    }
}
```

## 性能优化 / Performance Optimization

### 1. 内存管理

```javascript
// 定期清理内存
PerformanceOptimizer.cleanup();

// 检查内存使用
const memory = PerformanceOptimizer.getMemoryUsage();
console.log('内存使用:', memory);
```

### 2. 性能监控

```javascript
// 监控导出性能
const session = PerformanceOptimizer.startMonitoring('导出操作');
// ... 执行导出
const report = PerformanceOptimizer.endMonitoring(session);
console.log('性能报告:', report);
```

## 调试工具 / Debugging Tools

### 1. 基础功能测试

```javascript
// 测试基础功能
const results = testBasicFunctionality();
console.log('功能测试结果:', results);
```

### 2. 依赖检查

```javascript
// 检查外部依赖
const deps = checkExternalDependencies();
console.log('依赖状态:', deps);
```

### 3. 导出调试

```javascript
// 调试导出功能
debugImageExport();
```

## 兼容性 / Compatibility

### 支持的浏览器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 依赖库版本
- html2canvas: 1.4.1+
- jsPDF: 2.5.1+

## 故障排除 / Troubleshooting

### 1. 导出失败

**问题**: 导出时出现错误
**解决方案**:
1. 检查依赖库是否正确加载
2. 确认容器内容不为空
3. 检查浏览器控制台错误信息
4. 使用 `debugImageExport()` 进行诊断

### 2. 导出内容不完整

**问题**: 导出的PDF/图片内容缺失
**解决方案**:
1. 确保所有图片已加载完成
2. 检查CSS样式是否正确应用
3. 验证容器尺寸是否正确

### 3. 性能问题

**问题**: 导出速度慢或内存占用高
**解决方案**:
1. 减少页面内容复杂度
2. 优化图片大小和数量
3. 定期调用 `PerformanceOptimizer.cleanup()`

## 更新日志 / Changelog

### v1.0.0 (2024-12-21)
- 初始版本发布
- 支持PDF/PNG/JPEG导出
- 集成性能优化和错误处理
- 完整的调试工具

## 许可证 / License

MIT License - 详见项目根目录的 LICENSE 文件
